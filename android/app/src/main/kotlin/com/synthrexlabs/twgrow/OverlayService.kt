package com.synthrexlabs.twgrow

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.core.content.ContextCompat

class OverlayService : Service() {
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var timerText: TextView? = null
    private var cancelButton: Button? = null
    private var returnButton: Button? = null
    
    companion object {
        var onReturnCallback: (() -> Unit)? = null
        var onCancelCallback: (() -> Unit)? = null
        var isOverlayVisible = false
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        createOverlayView()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            "SHOW_OVERLAY" -> {
                val remainingSeconds = intent.getIntExtra("remainingSeconds", 15)
                showOverlay(remainingSeconds)
            }
            "UPDATE_OVERLAY" -> {
                val remainingSeconds = intent.getIntExtra("remainingSeconds", 15)
                updateOverlay(remainingSeconds)
            }
            "HIDE_OVERLAY" -> {
                hideOverlay()
            }
        }
        return START_NOT_STICKY
    }

    private fun createOverlayView() {
        overlayView = LayoutInflater.from(this).inflate(R.layout.overlay_layout, null)
        
        timerText = overlayView?.findViewById(R.id.timer_text)
        cancelButton = overlayView?.findViewById(R.id.cancel_button)
        returnButton = overlayView?.findViewById(R.id.return_button)
        
        cancelButton?.setOnClickListener {
            onCancelCallback?.invoke()
            hideOverlay()
        }
        
        returnButton?.setOnClickListener {
            onReturnCallback?.invoke()
            bringAppToForeground()
            hideOverlay()
        }
    }

    private fun showOverlay(remainingSeconds: Int) {
        if (isOverlayVisible || !canDrawOverlays()) return
        
        val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            layoutFlag,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )
        
        params.gravity = Gravity.BOTTOM
        
        updateOverlay(remainingSeconds)
        
        try {
            windowManager?.addView(overlayView, params)
            isOverlayVisible = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateOverlay(remainingSeconds: Int) {
        timerText?.text = "Returning to app in $remainingSeconds seconds"
    }

    private fun hideOverlay() {
        if (isOverlayVisible && overlayView != null) {
            try {
                windowManager?.removeView(overlayView)
                isOverlayVisible = false
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        stopSelf()
    }

    private fun canDrawOverlays(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }

    private fun bringAppToForeground() {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        hideOverlay()
    }
}
