package com.synthrexlabs.twgrow

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "twgrow/overlay"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "checkOverlayPermission" -> {
                    val hasPermission = checkOverlayPermission()
                    result.success(hasPermission)
                }
                "requestOverlayPermission" -> {
                    requestOverlayPermission()
                    result.success(null)
                }
                "showOverlay" -> {
                    val remainingSeconds = call.argument<Int>("remainingSeconds") ?: 15
                    showOverlay(remainingSeconds)
                    result.success(null)
                }
                "updateOverlay" -> {
                    val remainingSeconds = call.argument<Int>("remainingSeconds") ?: 15
                    updateOverlay(remainingSeconds)
                    result.success(null)
                }
                "hideOverlay" -> {
                    hideOverlay()
                    result.success(null)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Set up overlay callbacks
        OverlayService.onReturnCallback = {
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
                .invokeMethod("onReturnNow", null)
        }

        OverlayService.onCancelCallback = {
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
                .invokeMethod("onCancel", null)
        }
    }

    private fun checkOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }

    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:$packageName"))
            startActivity(intent)
        }
    }

    private fun showOverlay(remainingSeconds: Int) {
        val intent = Intent(this, OverlayService::class.java)
        intent.action = "SHOW_OVERLAY"
        intent.putExtra("remainingSeconds", remainingSeconds)
        startService(intent)
    }

    private fun updateOverlay(remainingSeconds: Int) {
        val intent = Intent(this, OverlayService::class.java)
        intent.action = "UPDATE_OVERLAY"
        intent.putExtra("remainingSeconds", remainingSeconds)
        startService(intent)
    }

    private fun hideOverlay() {
        val intent = Intent(this, OverlayService::class.java)
        intent.action = "HIDE_OVERLAY"
        startService(intent)
    }
}
