<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/overlay_background"
    android:layout_margin="16dp"
    android:padding="16dp">

    <!-- Header with app branding -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/header_background"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:layout_marginBottom="12dp">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_auto_mode"
            android:layout_marginEnd="6dp"
            android:layout_gravity="center_vertical" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="TwGrow Auto Follow"
            android:textColor="#9146FF"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_gravity="center_vertical" />

    </LinearLayout>

    <!-- Timer text -->
    <TextView
        android:id="@+id/timer_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Returning to app in 15 seconds"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp" />

    <!-- Buttons row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/cancel_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:background="@drawable/cancel_button_background"
            android:layout_marginEnd="6dp"
            android:paddingVertical="12dp" />

        <Button
            android:id="@+id/return_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Return Now"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:background="@drawable/return_button_background"
            android:layout_marginStart="6dp"
            android:paddingVertical="12dp" />

    </LinearLayout>

</LinearLayout>
