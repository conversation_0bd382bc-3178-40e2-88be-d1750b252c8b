import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/provider.dart';

class CampaignScreen extends StatefulWidget {
  const CampaignScreen({super.key});

  @override
  State<CampaignScreen> createState() => _CampaignScreenState();
}

class _CampaignScreenState extends State<CampaignScreen> {
  @override
  void initState() {
    final provider = Provider.of<Data>(context, listen: false);
    provider.refreshCampaigns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<Data>(context);
    List<Map<String, dynamic>> campaigns = provider.campaigns;
    return Container(
      color: const Color(0xFF0E0E1A),
      child: campaigns.isEmpty
          ? _buildEmptyState(context)
          : _buildCampaignList(campaigns),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFF282842),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.campaign_outlined,
              size: 48,
              color: Colors.white.withValues(alpha:0.5),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'campaigns.no_active_campaigns'.tr(),
            style: TextStyle(
              color: Colors.white.withValues(alpha:0.9),
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'campaigns.start_campaign'.tr(),
            style: TextStyle(
              color: Colors.white.withValues(alpha:0.5),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              final provider = Provider.of<Data>(context, listen: false);
              provider.changeIndex(2);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF9146FF),
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 16,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: Text(
              'campaigns.create_campaign'.tr(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCampaignList(List<Map<String, dynamic>> campaigns) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      itemCount: campaigns.length,
      itemBuilder: (context, index) =>
          _CampaignCard(campaign: campaigns[index]),
    );
  }
}

class _CampaignCard extends StatefulWidget {
  final Map<String, dynamic> campaign;

  const _CampaignCard({required this.campaign});

  @override
  State<_CampaignCard> createState() => _CampaignCardState();
}

class _CampaignCardState extends State<_CampaignCard> {
  void _showDeleteDialog(BuildContext context, String cid) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: const Color(0xFF282842),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha:0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.delete_outline,
                    color: Colors.red,
                    size: 28,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'campaigns.delete_campaign'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'campaigns.cannot_undo'.tr(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha:0.7),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF343450),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.amber.withValues(alpha:0.7),
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'campaigns.no_refund'.tr(),
                          style: TextStyle(
                            color: Colors.white.withValues(alpha:0.7),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                        ),
                        child: Text(
                          'campaigns.cancel'.tr(),
                          style: TextStyle(
                            color: Colors.white.withValues(alpha:0.7),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          final provider = Provider.of<Data>(context, listen: false);
                          provider.deleteCampaign(cid);
                          Navigator.of(context).pop();
                        },
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.red.withValues(alpha:0.1),
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'campaigns.delete'.tr(),
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  final loginBox = Hive.box("User");

  @override
  Widget build(BuildContext context) {
    final progress = widget.campaign['current'] / widget.campaign['target'];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF282842),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF343450),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: widget.campaign["username"] ==
                                (loginBox.get("loginname") ?? "")
                            ? const Color(0xFF9146FF).withValues(alpha:0.1)
                            : const Color(0xFF343450),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.account_circle_outlined,
                            size: 16,
                            color: widget.campaign["username"] ==
                                    (loginBox.get("loginname") ?? "")
                                ? const Color(0xFF9146FF)
                                : Colors.white.withValues(alpha:0.7),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '@${widget.campaign['username']}',
                            style: TextStyle(
                              color: widget.campaign["username"] ==
                                      (loginBox.get("loginname") ?? "")
                                  ? const Color(0xFF9146FF)
                                  : Colors.white.withValues(alpha:0.7),
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  widget.campaign['title'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Stack(
                  children: [
                    Container(
                      height: 6,
                      decoration: BoxDecoration(
                        color: const Color(0xFF343450),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    FractionallySizedBox(
                      widthFactor: progress,
                      child: Container(
                        height: 6,
                        decoration: BoxDecoration(
                          color: const Color(0xFF9146FF),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${widget.campaign['current']}/${widget.campaign['target']}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 12,
            right: 12,
            child: IconButton(
              icon: Icon(
                Icons.delete_outline,
                color: Colors.red.withValues(alpha:0.7),
                size: 20,
              ),
              onPressed: () => _showDeleteDialog(context, widget.campaign["cid"]),
            ),
          ),
        ],
      ),
    );
  }
}