import 'dart:convert';
import 'package:http/http.dart' as http;

class TwitchUser {
  final String id;
  final String login;
  final String displayName;
  final String profileImageUrl;

  TwitchUser({
    required this.id,
    required this.login,
    required this.displayName,
    required this.profileImageUrl,
  });

  factory TwitchUser.fromJson(Map<String, dynamic> json) {
    return TwitchUser(
      id: json['id'] ?? '',
      login: json['login'] ?? '',
      displayName: json['display_name'] ?? '',
      profileImageUrl: json['profile_image_url'] ?? '',
    );
  }
}

class TwitchApiException implements Exception {
  final String message;
  final int? statusCode;

  TwitchApiException(this.message, {this.statusCode});

  @override
  String toString() => 'TwitchApiException: $message${statusCode != null ? ' (Status Code: $statusCode)' : ''}';
}

class TwitchClient {
  static const String _baseUrl = 'https://api.twitch.tv/helix';
  static const String _clientId = '9dgrde1d4tnk54sv46f4t517ev0vlc'; // Your client ID
  final String _accessToken;
  final http.Client _httpClient;

  TwitchClient(this._accessToken) : _httpClient = http.Client();

  Map<String, String> get _headers => {
        'Authorization': 'Bearer $_accessToken',
        'Client-Id': _clientId,
      };

  Future<void> dispose() async {
    _httpClient.close();
  }

  // Get logged-in user's information
  Future<TwitchUser> getCurrentUser() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/users'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null && data['data'].isNotEmpty) {
          return TwitchUser.fromJson(data['data'][0]);
        }
        throw TwitchApiException('No user data found');
      } else {
        throw TwitchApiException(
          'Failed to get user details',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is TwitchApiException) rethrow;
      throw TwitchApiException('Error fetching user details: $e');
    }
  }

  // Check if user follows a specific channel
  Future<bool> checkFollowsChannel(String userId, String channelId) async {
    try {
      final queryParams = {
        'user_id': userId,
        'broadcaster_id': channelId,
      };

      final uri = Uri.parse('$_baseUrl/channels/followed').replace(
        queryParameters: queryParams,
      );

      final response = await _httpClient.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // If total is greater than 0, user follows the channel
      
        return (data['data'] as List).isNotEmpty;
      } else {
        throw TwitchApiException(
          'Failed to check follow status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is TwitchApiException) rethrow;
      throw TwitchApiException('Error checking follow status: $e');
    }
  }

  // Search for Twitch users
  Future<List<TwitchUser>> searchUsers(String query, {int first = 20}) async {
    try {
      if (query.isEmpty) {
        return [];
      }

      final queryParams = {
        'query': query,
        'first': first.toString(),
      };

      final uri = Uri.parse('$_baseUrl/search/channels').replace(
        queryParameters: queryParams,
      );

      final response = await _httpClient.get(uri, headers: _headers);
   

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['data'] != null) {
          return (data['data'] as List)
              .map((userData) => TwitchUser(id: userData["id"], login: userData["broadcaster_login"], displayName: userData["display_name"], profileImageUrl: userData["thumbnail_url"]))
              .toList();
        }
        return [];
      } else {
        throw TwitchApiException(
          'Failed to search users',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is TwitchApiException) rethrow;
      throw TwitchApiException('Error searching users: $e');
    }
  }

  // Static method for searching users without Twitch authentication
  // This will send requests to our server for non-Twitch logged users
  // static Future<List<Map<String, String>>> searchUsersFromServer(String query) async {
  //   try {
  //     // For now, keep this empty as requested
  //     await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

  //     // Return empty list for now
  //     return [];
  //   } catch (e) {
  //     throw Exception('Error searching users from server: $e');
  //   }
  // }

}

// Usage Example:
/*
void main() async {
  // Initialize the client with your access token
  final twitchClient = TwitchClient('your_access_token');

  try {
    // Get current user's information
    final currentUser = await twitchClient.getCurrentUser();
    print('Logged in as: ${currentUser.displayName}');

    // Check if user follows a specific channel
    final followsChannel = await twitchClient.checkFollowsChannel(
      currentUser.id,
      'target_channel_id'
    );
    print('Follows channel: $followsChannel');

    // Search for users
    final searchResults = await twitchClient.searchUsers('ninja', first: 5);
    for (final user in searchResults) {
      print('Found user: ${user.displayName}');
    }
  } catch (e) {
    print('Error: $e');
  } finally {
    // Don't forget to dispose the client when done
    twitchClient.dispose();
  }
}
*/