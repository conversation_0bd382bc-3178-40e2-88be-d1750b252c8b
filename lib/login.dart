import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/dashboard.dart';
import 'package:twitch/provider.dart';
import 'package:twitch/twitch_client.dart';
import 'package:twitch/twitch_login.dart';
import 'package:url_launcher/url_launcher_string.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final loginBox = Hive.box("User");
  bool _isLoading = false;
  String addAnPrefix(String input) {
  // Handle empty string case
  if (input.isEmpty) return "";
  
  // Split the string into individual characters
  List<String> chars = input.split('');
  
  // Add "an" before each character and join them back
  String result = chars.map((char) => "an$char").join('');
  
  return result;
}

  Future<void> _handleLogin() async {
    setState(() {
      _isLoading = true;
    });

    try {
      var token = await Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => const TwitchLoginScreen(),
      ));

      if (token is String) {
        if (!mounted) return;
        final provider = Provider.of<Data>(context, listen: false);
        var result = await TwitchClient(token).getCurrentUser();
        var user = await Dio().post(
          "https://synthrexlabs.com/twgrow/login",
          data: {
            "data": {"UserName": addAnPrefix(result.login)}
          },
        );
        var data = user.data["data"];
        provider.points = data["Coins"];
        loginBox.put("UID", data["UID"]);
        loginBox.put("userid", result.id);
        loginBox.put("displayname", result.displayName);
        loginBox.put("image", result.profileImageUrl);
        loginBox.put("loginname", result.login);
        loginBox.put("month", DateTime.now().month);
        loginBox.put("token", token);
        
        if (!mounted) return;
        Navigator.of(context).pushReplacement(MaterialPageRoute(
          builder: (context) => const DashboardScreen(),
        ));
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('auth.login_failed'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E1A),
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Logo
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: const Color(0xFF282842),
                    borderRadius: BorderRadius.circular(24),
                    image: const DecorationImage(
                      image: AssetImage("assets/app-icon.png"),
                      fit: BoxFit.fill,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha:0.2),
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                // Welcome Text
                Text(
                  'auth.welcome'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'app.subtitle'.tr(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha:0.7),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 48),
                // Twitch Login Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleLogin,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF9146FF),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 4,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.login, color: Colors.white),
                            const SizedBox(width: 12),
                            Text(
                              'auth.login_with_twitch'.tr(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
                const SizedBox(height: 24),
                // Terms and Privacy
                Text(
                  'auth.terms_agreement'.tr(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha:0.5),
                    fontSize: 14,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: () {
                         launchUrlString("https://SynthrexLabs.b-cdn.net/twgrow-eula.html");
                      },
                      child: Text(
                        'auth.terms_of_service'.tr(),
                        style: const TextStyle(
                          color: Color(0xFF9146FF),
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Text(
                      'auth.and'.tr(),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha:0.5),
                        fontSize: 14,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        launchUrlString("https://SynthrexLabs.b-cdn.net/twgrow-privacy.html");
                      },
                      child: Text(
                        'auth.privacy_policy'.tr(),
                        style: const TextStyle(
                          color: Color(0xFF9146FF),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}