import 'dart:isolate';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';

// This is the overlay widget that will be shown as a system overlay
@pragma("vm:entry-point")
void overlayMain() {
  runApp(const MaterialApp(
    debugShowCheckedModeBanner: false,
    home: OverlayWidget(),
  ));
}

class OverlayWidget extends StatefulWidget {
  const OverlayWidget({super.key});

  @override
  State<OverlayWidget> createState() => _OverlayWidgetState();
}

class _OverlayWidgetState extends State<OverlayWidget> {
  int _remainingSeconds = 15;
  static const String _foregroundPortName = "foreground_port";

  @override
  void initState() {
    super.initState();
    _startTimer();
    _listenToMainApp();
  }

  void _listenToMainApp() {
    FlutterOverlayWindow.overlayListener.listen((data) {
      if (data is Map && data.containsKey('remainingSeconds')) {
        setState(() {
          _remainingSeconds = data['remainingSeconds'];
        });
      }
    });
  }

  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _remainingSeconds--;
        });

        if (_remainingSeconds <= 0) {
          _sendMessage("return_now");
        } else {
          _startTimer();
        }
      }
    });
  }

  void _sendMessage(String message) {
    SendPort? port = IsolateNameServer.lookupPortByName(_foregroundPortName);
    port?.send(message);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFF282842),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Returning to app in $_remainingSeconds seconds',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _sendMessage("cancel"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _sendMessage("return_now"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF9146FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Return Now',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
