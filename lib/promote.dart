import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/provider.dart';
import 'package:twitch/twitch_client.dart';
import 'package:twitch/rate.dart';

class PromoteScreen extends StatefulWidget {
  const PromoteScreen({super.key});

  @override
  PromoteScreenState createState() => PromoteScreenState();
}

class PromoteScreenState extends State<PromoteScreen> {
  String selectedProfile = '';
  String selectedUserId = '';
  String selectedProfileImage = '';
  bool isCreatingCampaign = false;
  final loginBox = Hive.box("User");

  Future<bool> _createCampaign(Map<String, dynamic> package) async {
    try {
      final provider = Provider.of<Data>(context, listen: false);
      String uid = loginBox.get("UID") ?? "";
      var result =
          await Dio().post("https://synthrexlabs.com/twgrow/promote", data: {
        "data": {
          "Package": package['name'],
          "UserName": selectedProfile,
          "UserId": selectedUserId,
          "ProfileImage": selectedProfileImage,
          "UID": uid
        }
      });
      provider.addCampaign(package['name'], selectedProfile, 0,
          package["followers"], result.data["data"]["CID"]);
      provider.addOfflinePoints(-(package["coins"] as int));

      return true;
    } catch (error) {
      return false;
    }
  }

  @override
  void initState() {
    // Check if there's a stored selected channel first
    final provider = Provider.of<Data>(context, listen: false);
    final storedChannel = provider.getSelectedChannel();

    if (storedChannel['username']!.isNotEmpty) {
      // Use stored channel
      selectedProfile = storedChannel['username']!;
      selectedUserId = storedChannel['userId']!;
      selectedProfileImage = storedChannel['profileImage']!;
    }
    // If no stored channel, leave selectedProfile empty to show search screen
    super.initState();
  }

  void _showSuccessDialog(BuildContext context, Map<String, dynamic> package) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      barrierDismissible: false,
      builder: (BuildContext context) => Dialog(
        backgroundColor: const Color(0xFF282842),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        child: Container(
          padding: const EdgeInsets.all(15),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 72,
                height: 72,
                decoration: BoxDecoration(
                  color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(36),
                ),
                child: const Icon(
                  Icons.check_circle_outline,
                  color: Color(0xFF9146FF),
                  size: 36,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'promote.campaign_created'.tr(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'promote.campaign_success'.tr(args: [
                  package['name'],
                ]),
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF343450),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.person_add,
                          color: Color(0xFF9146FF),
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'promote.followers_count'.tr(args: [
                            package['followers'].toString(),
                          ]),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.schedule,
                            color: Color(0xFF9146FF),
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'promote.active'.tr(),
                            style: const TextStyle(
                              color: Color(0xFF9146FF),
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _checkAndShowRatingDialogAfterCampaign();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF9146FF),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'promote.done'.tr(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _checkAndShowRatingDialogAfterCampaign() async {
    final bool hasRated = loginBox.get("hasRated") ?? false;

    // Show rating dialog after successful campaign creation if user hasn't rated
    if (!hasRated) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => RateAppDialog(
              onRated: () async {
                await loginBox.put("hasRated", true);
              },
            ),
          );
        }
      });
    }
  }

  final List<Map<String, dynamic>> packages = [
    {
      'name': 'Starter',
      'followers': 25,
      'coins': 500,
      'discount': 0,
      'tag': 'STARTER',
    },
    {
      'name': 'Growth',
      'followers': 100,
      'coins': 1500,
      'discount': 0,
      'tag': 'GROWTH',
      'isPopular': true,
    },
    {
      'name': 'Accelerate',
      'followers': 300,
      'coins': 4000,
      'discount': 0,
      'tag': 'ACCELERATE',
    },
    {
      'name': 'Pro',
      'followers': 600,
      'coins': 7500,
      'discount': 0,
      'tag': 'PRO',
    },
    {
      'name': 'Ultimate',
      'followers': 1000,
      'coins': 12000,
      'discount': 0,
      'tag': 'ULTIMATE',
    },
  ];

  void _showSearchDialog() {
    // Check if user has Twitch token
    String? token = loginBox.get("token");

    if (token != null && token.isNotEmpty) {
      // User has Twitch login, show Twitch search dialog
      showDialog(
        context: context,
        barrierColor: Colors.black87,
        builder: (BuildContext context) =>
            SearchProfileDialog((TwitchUser user) {
          setState(() {
            selectedProfile = user.login;
            selectedProfileImage = user.profileImageUrl;
            selectedUserId = user.id;
          });
          // Store the selected channel
          final provider = Provider.of<Data>(context, listen: false);
          provider.storeSelectedChannel(
              user.login, user.id, user.profileImageUrl);
        }),
      );
    } else {
      // User doesn't have Twitch login, show server search dialog
      showDialog(
        context: context,
        barrierColor: Colors.black87,
        builder: (BuildContext context) =>
            ServerSearchProfileDialog((TwitchUser user) {
          setState(() {
            selectedProfile = user.login;
            selectedProfileImage = user.profileImageUrl;
            selectedUserId = user.id;
          });
          // Store the selected channel
          final provider = Provider.of<Data>(context, listen: false);
          provider.storeSelectedChannel(
              user.login, user.id, user.profileImageUrl);
        }),
      );
    }
  }

  Widget _buildChannelSearchScreen() {
    return Container(
      color: const Color(0xFF0E0E1A),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main icon and title section
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: const Color(0xFF282842),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 16,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Icon with animated background
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color:
                                const Color(0xFF9146FF).withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.rocket_launch,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Title
                    Text(
                      'promote.promote_your_channel'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    // Description
                    Text(
                      'promote.search_description'.tr(),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 14,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 40),

              // Search button
              Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF9146FF).withValues(alpha: 0.4),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _showSearchDialog,
                    borderRadius: BorderRadius.circular(14),
                    child: Center(
                      child: Text(
                        'promote.search_channels'.tr(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 30),
              // Bottom features
              Row(
                children: [
                  Expanded(
                    child: _buildFeatureItem(
                      Icons.save,
                      'promote.auto_save'.tr(),
                      'promote.selection_remembered'.tr(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildFeatureItem(
                      Icons.swap_horiz,
                      'promote.easy_switch'.tr(),
                      'promote.change_anytime'.tr(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildFeatureItem(
                      Icons.trending_up,
                      'promote.grow_fast'.tr(),
                      'promote.boost_followers'.tr(),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF282842).withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: const Color(0xFF9146FF).withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF9146FF).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF9146FF),
              size: 16,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 9,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<Data>(context);
    int userCoins = provider.points;

    // Check if no channel has been selected
    final storedChannel = provider.getSelectedChannel();
    bool hasSelectedChannel = storedChannel['username']!.isNotEmpty;

    return Container(
      color: const Color(0xFF0E0E1A),
      child: hasSelectedChannel
          ? ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              children: [
                _buildProfileSelector(),
                const SizedBox(height: 24),
                ...packages
                    .map((package) => _buildPackageCard(package, userCoins)),
              ],
            )
          : _buildChannelSearchScreen(),
    );
  }

  Widget _buildProfileSelector() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF282842), Color(0xFF32324D)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showSearchDialog,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                    ),
                    borderRadius: BorderRadius.circular(14),
                    image: DecorationImage(
                      image: NetworkImage(selectedProfileImage),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF9146FF).withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "@$selectedProfile",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (selectedProfile == loginBox.get("loginname")) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                const Color(0xFF9146FF).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.verified,
                                color: Color(0xFF9146FF),
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'promote.your_account'.tr(),
                                style: const TextStyle(
                                  color: Color(0xFF9146FF),
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF343450),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.search,
                    color: Colors.white54,
                    size: 18,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPackageCard(Map<String, dynamic> package, int userCoins) {
    final isPopular = package['isPopular'] == true;
    final discountedCoins =
        (package['coins'] * (100 - package['discount']) / 100).round();
    final hasEnoughCoins = userCoins >= discountedCoins;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: isPopular
            ? const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
              )
            : null,
        color: isPopular ? null : const Color(0xFF282842),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isPopular
                ? const Color(0xFF9146FF).withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _showConfirmDialog(
                package, hasEnoughCoins, discountedCoins, userCoins);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isPopular)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.local_fire_department,
                                color: Colors.white,
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'packages.growth.tag'.tr(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 8),
                      Text(
                        'packages.${package['name'].toLowerCase()}.name'.tr(),
                        style: TextStyle(
                          color: isPopular
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.9),
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.person_add,
                            color: isPopular
                                ? Colors.white.withValues(alpha: 0.7)
                                : const Color(0xFF9146FF),
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'promote.followers_count'
                                .tr(args: [package['followers'].toString()]),
                            style: TextStyle(
                              color: isPopular
                                  ? Colors.white.withValues(alpha: 0.7)
                                  : Colors.white.withValues(alpha: 0.6),
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isPopular
                        ? Colors.white.withValues(alpha: 0.2)
                        : const Color(0xFF343450),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.monetization_on,
                        color:
                            isPopular ? Colors.white : const Color(0xFF9146FF),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        discountedCoins.toString(),
                        style: TextStyle(
                          color: isPopular
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.9),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showConfirmDialog(Map<String, dynamic> package, bool hasEnoughCoins,
      int discountedCoins, int userCoins) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          backgroundColor: const Color(0xFF282842),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          child: Container(
            padding: const EdgeInsets.all(15),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.campaign_outlined,
                        color: Color(0xFF9146FF),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'promote.confirm_campaign'.tr(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'packages.${package['name'].toLowerCase()}.name'
                                .tr(),
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF343450),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFF9146FF)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.person_add,
                              color: Color(0xFF9146FF),
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'promote.target_followers'.tr(),
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7),
                                fontSize: 12,
                              ),
                            ),
                          ),
                          Text(
                            package['followers'].toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.amber.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.monetization_on,
                              color: Colors.amber,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'promote.cost'.tr(),
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7),
                                fontSize: 12,
                              ),
                            ),
                          ),
                          Text(
                            'promote.coins_amount'
                                .tr(args: [discountedCoins.toString()]),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: isCreatingCampaign
                            ? null
                            : () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'promote.cancel'.tr(),
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: isCreatingCampaign
                            ? null
                            : () async {
                                if (hasEnoughCoins) {
                                  setState(() {
                                    isCreatingCampaign = true;
                                  });

                                  final success =
                                      await _createCampaign(package);

                                  setState(() {
                                    isCreatingCampaign = false;
                                  });

                                  if (success) {
                                    if (!context.mounted) return;
                                    Navigator.pop(context);
                                    _showSuccessDialog(context, package);
                                  } else {
                                    if (!context.mounted) return;
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'promote.creation_failed'.tr(),
                                          style: const TextStyle(
                                              color: Colors.white),
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } else {
                                  Navigator.pop(context);
                                  final provider =
                                      Provider.of<Data>(context, listen: false);
                                  provider.changeIndex(3);
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: hasEnoughCoins
                              ? const Color(0xFF9146FF)
                              : Colors.amber,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (isCreatingCampaign) ...[
                              const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'promote.creating'.tr(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ] else ...[
                              Text(
                                hasEnoughCoins
                                    ? 'promote.promote'.tr()
                                    : 'promote.buy_coins'.tr(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ]
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                if (!hasEnoughCoins) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: Colors.amber,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'promote.need_more_coins'.tr(args: [
                              (discountedCoins - userCoins).toString()
                            ]),
                            style: const TextStyle(
                              color: Colors.amber,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SearchProfileDialog extends StatefulWidget {
  final Function updateProfile;
  const SearchProfileDialog(this.updateProfile, {super.key});

  @override
  SearchProfileDialogState createState() => SearchProfileDialogState();
}

class SearchProfileDialogState extends State<SearchProfileDialog> {
  bool isLoading = false;
  String searchQuery = '';
  List<TwitchUser> searchResults = [];
  bool hasPerformedSearch = false;
  final _focusNode = FocusNode();
  final _searchController = TextEditingController();
  late TwitchClient _twitchClient;
  final loginBox = Hive.box("User");

  @override
  void initState() {
    super.initState();
    _twitchClient = TwitchClient(loginBox.get("token") ?? "");
    Future.delayed(const Duration(milliseconds: 200), () {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _searchController.dispose();
    _twitchClient.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    if (searchQuery.isEmpty) return;

    setState(() {
      isLoading = true;
      hasPerformedSearch = true;
    });

    try {
      final results = await _twitchClient.searchUsers(searchQuery);
      setState(() {
        searchResults = results;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'promote.search_error'.tr(args: [e.toString()]),
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: const Color(0xFF282842),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 400,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 15, 20, 16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'promote.search_profile'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white54,
                      size: 25,
                    ),
                    style: IconButton.styleFrom(
                      iconSize: 25,
                      backgroundColor: const Color(0xFF343450),
                      padding: const EdgeInsets.all(5),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: TextField(
                controller: _searchController,
                focusNode: _focusNode,
                onChanged: (value) {
                  setState(() => searchQuery = value);
                },
                onSubmitted: (_) => _performSearch(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  hintText: 'promote.enter_username'.tr(),
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                    fontSize: 16,
                  ),
                  suffixIcon: IconButton(
                    icon: const Icon(
                      Icons.search,
                      color: Colors.white54,
                      size: 20,
                    ),
                    onPressed: _performSearch,
                  ),
                  filled: true,
                  fillColor: const Color(0xFF343450),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: isLoading
                    ? const Center(
                        child: Padding(
                          padding: EdgeInsets.all(24),
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Color(0xFF9146FF),
                            ),
                            strokeWidth: 3,
                          ),
                        ),
                      )
                    : searchResults.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 8,
                            ),
                            shrinkWrap: true,
                            itemCount: searchResults.length,
                            itemBuilder: (context, index) {
                              final user = searchResults[index];
                              return _buildProfileTile(user);
                            },
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    if (!hasPerformedSearch) {
      return Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.search,
                size: 48,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              const SizedBox(height: 16),
              Text(
                'promote.search_prompt'.tr(),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.person_off,
              size: 48,
              color: Colors.white.withValues(alpha: 0.2),
            ),
            const SizedBox(height: 16),
            Text(
              'promote.no_profiles'.tr(),
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileTile(TwitchUser user) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          widget.updateProfile(user);
          Navigator.pop(context);
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(24),
                  image: DecorationImage(
                    image: NetworkImage(user.profileImageUrl),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '@${user.login}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ServerSearchProfileDialog extends StatefulWidget {
  final Function updateProfile;
  const ServerSearchProfileDialog(this.updateProfile, {super.key});

  @override
  ServerSearchProfileDialogState createState() =>
      ServerSearchProfileDialogState();
}

class ServerSearchProfileDialogState extends State<ServerSearchProfileDialog> {
  bool isLoading = false;
  String searchQuery = '';
  List<TwitchUser> searchResults = [];
  bool hasPerformedSearch = false;
  final _focusNode = FocusNode();
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 200), () {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    if (searchQuery.isEmpty) return;

    setState(() {
      isLoading = true;
      hasPerformedSearch = true;
    });

    try {
      var result = await Dio()
          .get("https://synthrexlabs.com/twgrow/search/$searchQuery");

      setState(() {
        searchResults = (result.data["data"] as List)
            .map(
              (e) => TwitchUser(
                  id: e["id"],
                  login: e["broadcaster_login"],
                  displayName: e["display_name"],
                  profileImageUrl: e["thumbnail_url"]),
            )
            .toList();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'promote.search_error'.tr(args: [""]),
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: const Color(0xFF282842),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'promote.search_profile'.tr(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white54,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _searchController,
              focusNode: _focusNode,
              style: const TextStyle(color: Colors.white),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              onSubmitted: (_) => _performSearch(),
              decoration: InputDecoration(
                hintText: 'promote.enter_username'.tr(),
                hintStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(
                    Icons.search,
                    color: Color(0xFF9146FF),
                  ),
                  onPressed: _performSearch,
                ),
                filled: true,
                fillColor: const Color(0xFF343450),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: isLoading
                    ? const Center(
                        child: Padding(
                          padding: EdgeInsets.all(24),
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Color(0xFF9146FF),
                            ),
                            strokeWidth: 3,
                          ),
                        ),
                      )
                    : searchResults.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 8,
                            ),
                            shrinkWrap: true,
                            itemCount: searchResults.length,
                            itemBuilder: (context, index) {
                              final user = searchResults[index];
                              return _buildProfileTile(user);
                            },
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    if (!hasPerformedSearch) {
      return Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.search,
                size: 48,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              const SizedBox(height: 16),
              Text(
                'promote.search_prompt'.tr(),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.person_off,
              size: 48,
              color: Colors.white.withValues(alpha: 0.2),
            ),
            const SizedBox(height: 16),
            Text(
              'promote.no_profiles'.tr(),
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileTile(TwitchUser user) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          widget.updateProfile(user);
          Navigator.pop(context);
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(24),
                  image: DecorationImage(
                    image: NetworkImage(user.profileImageUrl),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '@${user.login}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
