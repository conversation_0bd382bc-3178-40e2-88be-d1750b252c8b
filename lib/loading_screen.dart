import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:twitch/dashboard.dart';
import 'package:twitch/provider.dart';
import 'package:twitch/twitch_client.dart';
import 'package:twitch/twitch_login.dart';

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen> {
  final loginBox = Hive.box("User");
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    try {
      // Check if user is already logged in (either with Twitch or auto-login)
      String? uid = loginBox.get("UID");
      String? prevToken = loginBox.get("token");

      if (uid != null && uid.isNotEmpty) {
        if (prevToken != null && prevToken.isNotEmpty) {
          await Future.delayed(const Duration( milliseconds: 500));
          if (!mounted) return;
          var token = await Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => const TwitchLoginScreen(),
          ));

          if (token is String) {
            var result = await TwitchClient(token).getCurrentUser();
            loginBox.put("userid", result.id);
            loginBox.put("displayname", result.displayName);
            loginBox.put("image", result.profileImageUrl);
            loginBox.put("loginname", result.login);
            loginBox.put("month", DateTime.now().month);
            loginBox.put("token", token);
            if (!mounted) return;
            Navigator.of(context).pushReplacement(MaterialPageRoute(
              builder: (context) => const DashboardScreen(),
            ));
          }
        } else {
          loginBox.put("month", DateTime.now().month);
          if (!mounted) return;
          Navigator.of(context).pushReplacement(MaterialPageRoute(
            builder: (context) => const DashboardScreen(),
          ));
        }
        return;
      }

      // User is not logged in, send request to /twgrow/loginNew
      final response =
          await Dio().post("https://synthrexlabs.com/twgrow/loginNew");

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data["data"];
        if (data != null) {
          if (!mounted) return;
          // Store UID and coins
          final provider = Provider.of<Data>(context, listen: false);
          provider.points = data["Coins"] ?? 0;
          loginBox.put("UID", data["UID"] ?? "");
          loginBox.put("month", DateTime.now().month);

          if (!mounted) return;
          Navigator.of(context).pushReplacement(MaterialPageRoute(
            builder: (context) => const DashboardScreen(),
          ));
        } else {
          throw Exception('Invalid response data');
        }
      } else {
        throw Exception('Failed to get login data');
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _errorMessage = 'loading.connection_failed'.tr();
      });
    }
  }

  void _retryLogin() {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    _checkLoginStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E1A),
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Logo/Title
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: const Color(0xFF282842),
                    borderRadius: BorderRadius.circular(24),
                    image: const DecorationImage(
                      image: AssetImage("assets/app-icon.png"),
                      fit: BoxFit.fill,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),
                const Text(
                  'TwGrow',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 60),

                if (_isLoading) ...[
                  const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF9146FF)),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'loading.setting_up_account'.tr(),
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                ] else ...[
                  Text(
                    _errorMessage,
                    style: const TextStyle(
                      color: Colors.red,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 30),
                  ElevatedButton(
                    onPressed: _retryLogin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF9146FF),
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: Text(
                      'loading.retry'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
