import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:easy_localization/easy_localization.dart';

class TwitchLoginScreen extends StatefulWidget {
  const TwitchLoginScreen({super.key});

  @override
  TwitchLoginScreenState createState() => TwitchLoginScreenState();
}

class TwitchLoginScreenState extends State<TwitchLoginScreen> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // Replace with your actual Twitch API credentials
  static const String clientId = '9dgrde1d4tnk54sv46f4t517ev0vlc';
  static const String redirectUri = 'https://twgrow.app';

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }


  void _initializeWebView() {
    final String authUrl = 'https://id.twitch.tv/oauth2/authorize'
        '?client_id=$clientId'
        '&redirect_uri=$redirectUri'
        '&response_type=token'
        '&scope=user:read:follows';

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // Updated user agent string to a more widely supported version
      ..setUserAgent('Mozilla/5.0 (Linux; Android 13; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36')
      ..enableZoom(false)
      ..setBackgroundColor(const Color(0xFF0E0E1A))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
              _errorMessage = '';
            });
          },
          onPageFinished: (String url) async {
            setState(() => _isLoading = false);
            
            // Inject CSS to force desktop mode
            await _controller.runJavaScript('''
              var meta = document.createElement('meta');
              meta.name = 'viewport';
              meta.content = 'width=1024, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
              document.getElementsByTagName('head')[0].appendChild(meta);
            ''');
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(redirectUri)) {
              final uri = Uri.parse(request.url);
              final fragment = uri.fragment;
              
              if (fragment.isNotEmpty) {
                final params = Uri.splitQueryString(fragment);
                final accessToken = params['access_token'];
                
                if (accessToken != null) {
                  _handleAuthSuccess(accessToken);
                  return NavigationDecision.prevent;
                }
              }
            }
            return NavigationDecision.navigate;
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _hasError = true;
              _errorMessage = 'twitch_login.load_error'.tr();
            });
          },
        ),
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
              _errorMessage = '';
            });
          },
          onPageFinished: (String url) {
            setState(() => _isLoading = false);
            
            // Inject JavaScript to detect browser support issues
            _controller.runJavaScript('''
              if (document.body.innerText.includes('browser is not currently supported')) {
                window.flutter_inappwebview.callHandler('onBrowserNotSupported');
              }
            ''');
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(redirectUri)) {
              final uri = Uri.parse(request.url);
              final fragment = uri.fragment;
              
              if (fragment.isNotEmpty) {
                final params = Uri.splitQueryString(fragment);
                final accessToken = params['access_token'];
                
                if (accessToken != null) {
                  _handleAuthSuccess(accessToken);
                  return NavigationDecision.prevent;
                }
              }
            }
            return NavigationDecision.navigate;
          },
          onWebResourceError: (WebResourceError error) {
           
          },
        ),
      )
      // Add JavaScript channel to handle browser support detection
      ..addJavaScriptChannel(
        'flutter_inappwebview',
        onMessageReceived: (JavaScriptMessage message) {
          if (message.message == 'onBrowserNotSupported') {
            setState(() {
              _hasError = true;
              _errorMessage = 'twitch_login.browser_error'.tr();
            });
          }
        },
      )
      ..loadRequest(
        Uri.parse(authUrl),
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Sec-Ch-Ua': '"Not;A=Brand";v="99", "Chromium";v="112"',
          'Sec-Ch-Ua-Mobile': '?1',
          'Sec-Ch-Ua-Platform': '"Android"',
          'Upgrade-Insecure-Requests': '1',
          'User-Agent': 'Mozilla/5.0 (Linux; Android 13; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-User': '?1',
          'Sec-Fetch-Dest': 'document',
        },
      );
  }
  void _handleAuthSuccess(String accessToken) {
    Navigator.of(context).pop(accessToken);
  }

  void _retryAuthentication() {
    setState(() {
      _hasError = false;
      _errorMessage = '';
      _isLoading = true;
    });
    _controller.reload();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E1A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF282842),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(
        children: [
          if (!_hasError) WebViewWidget(controller: _controller),
          if (_isLoading)
            Container(
              color: const Color(0xFF0E0E1A),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF9146FF)),
                ),
              ),
            ),
          if (_hasError)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _retryAuthentication,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF9146FF),
                      ),
                      child: Text('twitch_login.retry'.tr()),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}