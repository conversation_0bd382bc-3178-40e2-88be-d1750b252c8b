import 'dart:math' as math;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/provider.dart';
import 'package:twitch/twitch_client.dart';
import 'package:twitch/twitch_login.dart';
import 'package:twitch/rate.dart';

class SimplifiedF4FScreen extends StatefulWidget {
  const SimplifiedF4FScreen({super.key});

  @override
  SimplifiedF4FScreenState createState() => SimplifiedF4FScreenState();
}

class SimplifiedF4FScreenState extends State<SimplifiedF4FScreen>
    with WidgetsBindingObserver {
  final PageController _pageController = PageController();
  bool _isFollowing = false;
  bool _isAddingCoins = false;
  late TwitchClient _twitchClient;
  int? _pendingFollowIndex;
  bool _isClaimingBonus = false;
  bool _showWelcomeBonus = false;
  bool _autoFollowEnabled = false;
  bool _showAutoFollowWidget = false;
  int _currentAutoFollowIndex = 0;
  bool _isAutoFollowActive = false;
  DateTime? _lastAppResumeTime;
  bool _showFollowBonusWidget = false;
  int _followBonusProgress = 0;
  bool _isClaimingDailyBonus = false;

  final List<Map<String, dynamic>> _streamers = [];
  List<String> following = [];
  bool firstFollow = true; // Reset each app session

  @override
  void initState() {
    initFollowing();
    getStreamers();
    _checkWelcomeBonus();
    _checkAutoFollowWidget();
    _checkBonuses();
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initTwitchClient();
  }

  final followBox = Hive.box("Follow");
  void initFollowing() {
    following = followBox.get("Follow") ?? [];
  }

  void _checkWelcomeBonus() {
    final bool bonusClaimed = loginBox.get("welcomeBonusClaimed") ?? false;
    setState(() {
      _showWelcomeBonus = !bonusClaimed;
    });
  }

  void _checkAutoFollowWidget() {
    // Check if user is logged in with Twitch
    String? token = loginBox.get("token");
    bool isTwitchLoggedIn = token != null && token.isNotEmpty;

    if (isTwitchLoggedIn) {
      // Auto follow is always disabled when app starts
      setState(() {
        _showAutoFollowWidget = true;
        _autoFollowEnabled = false;
      });
    } else {
      setState(() {
        _showAutoFollowWidget = false;
        _autoFollowEnabled = false;
      });
    }
  }

  void _checkBonuses() async {
    try {
      String uid = loginBox.get("UID") ?? "";
      if (uid.isEmpty) return;

      final response =
          await Dio().get("https://synthrexlabs.com/twgrow/bonus/$uid");

      if (response.statusCode == 200 && response.data["status"] == "Success") {
        final bonuses = response.data["bonuses"];

        // Check daily bonus - available for all users
        if (bonuses["DailyBonusCompleted"] == false) {
          _claimDailyBonus();
        }

        // Check follow bonus - only for Twitch logged users
        String? token = loginBox.get("token");
        bool isTwitchLoggedIn = token != null && token.isNotEmpty;

        if (isTwitchLoggedIn) {
          if (bonuses["FollowBonusCompleted"] == false) {
            // Get current follow count from storage (separate from following list)
            final int currentFollowCount =
                loginBox.get("followBonusCount") ?? 0;
            setState(() {
              _showFollowBonusWidget = true;
              _followBonusProgress =
                  currentFollowCount % 30; // Progress towards next bonus
            });
          } else {
            setState(() {
              _showFollowBonusWidget = false;
            });
          }
        } else {
          setState(() {
            _showFollowBonusWidget = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error checking bonuses: $e');
    }
  }

  void _checkFollowBonusForTwitchUser() async {
    try {
      String uid = loginBox.get("UID") ?? "";
      if (uid.isEmpty) return;

      final response =
          await Dio().get("https://synthrexlabs.com/twgrow/bonus/$uid");

      if (response.statusCode == 200 && response.data["status"] == "Success") {
        final bonuses = response.data["bonuses"];

        // Check follow bonus for newly logged in Twitch user
        if (bonuses["FollowBonusCompleted"] == false) {
          final int currentFollowCount = loginBox.get("followBonusCount") ?? 0;
          setState(() {
            _showFollowBonusWidget = true;
            _followBonusProgress =
                currentFollowCount % 30; // Progress towards next bonus
          });
        }
      }
    } catch (e) {
      debugPrint('Error checking follow bonus for Twitch user: $e');
    }
  }

  Future<void> _claimDailyBonus() async {
    if (_isClaimingDailyBonus) return;

    setState(() {
      _isClaimingDailyBonus = true;
    });

    try {
      String uid = loginBox.get("UID") ?? "";
      final provider = Provider.of<Data>(context, listen: false);

      // Add 50 coins for daily bonus
      await provider.addPoints(50);

      // Mark daily bonus as completed
      await Dio()
          .get("https://synthrexlabs.com/twgrow/completedDailyBonus/$uid");

      // Show animated dialog
      if (mounted) {
        _showDailyBonusDialog();
      }
    } catch (e) {
      debugPrint('Error claiming daily bonus: $e');
    } finally {
      setState(() {
        _isClaimingDailyBonus = false;
      });
    }
  }

  void _showDailyBonusDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF282842), Color(0xFF32324D)],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Animated coin icon
                TweenAnimationBuilder(
                  duration: const Duration(milliseconds: 800),
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  builder: (context, double value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF9146FF)
                                  .withValues(alpha: 0.4),
                              blurRadius: 16,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.monetization_on,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 24),
                Text(
                  'daily_bonus.title'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'daily_bonus.description'.tr(args: ['50']),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Container(
                  width: double.infinity,
                  height: 52,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      borderRadius: BorderRadius.circular(16),
                      child: Center(
                        child: Text(
                          'daily_bonus.claim_button'.tr(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _updateFollowBonusProgress() async {
    if (!_showFollowBonusWidget) return;

    // Increment the follow bonus counter
    final int currentFollowCount = loginBox.get("followBonusCount") ?? 0;
    final int newFollowCount = currentFollowCount + 1;
    await loginBox.put("followBonusCount", newFollowCount);

    setState(() {
      _followBonusProgress = newFollowCount % 30;
    });

    // Check if bonus is completed (30 follows)
    if (newFollowCount % 30 == 0) {
      await _completeFollowBonus();
    }
  }

  Future<void> _completeFollowBonus() async {
    try {
      String uid = loginBox.get("UID") ?? "";
      final provider = Provider.of<Data>(context, listen: false);

      // Add 100 coins for follow bonus
      await provider.addPoints(100);

      // Mark follow bonus as completed
      await Dio()
          .get("https://synthrexlabs.com/twgrow/completedFollowBonus/$uid");

      // Reset the follow bonus counter
      await loginBox.put("followBonusCount", 0);

      // Show completion animation and hide widget
      if (mounted) {
        _showFollowBonusCompletionAnimation();
      }
    } catch (e) {
      debugPrint('Error completing follow bonus: $e');
    }
  }

  void _showFollowBonusCompletionAnimation() {
    // Hide the widget instantly
    setState(() {
      _showFollowBonusWidget = false;
    });

    // Show success snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.celebration, color: Color(0xFF9146FF)),
            const SizedBox(width: 8),
            Expanded(child: Text('follow_bonus.completed'.tr(args: ['100']))),
          ],
        ),
        backgroundColor: const Color(0xFF282842),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _toggleAutoFollow(bool value) async {
    setState(() {
      _autoFollowEnabled = value;
    });

    if (value) {
      // Start auto follow process
      _startAutoFollow();
    } else {
      // Stop auto follow process
      _stopAutoFollow();
      // Navigate to next unfollowed streamer
      _navigateToNextUnfollowed();
    }
  }

  void _startAutoFollow() {
    if (_streamers.isEmpty) return;

    setState(() {
      _isAutoFollowActive = true;
      _currentAutoFollowIndex = 0;
    });

    _processNextAutoFollow();
  }

  void _stopAutoFollow() {
    setState(() {
      _isAutoFollowActive = false;
      _pendingFollowIndex = null;
    });
  }

  void _navigateToNextUnfollowed() {
    if (_streamers.isEmpty) return;

    // Find the next unfollowed streamer starting from current position
    int targetIndex = -1;

    // First, try to find an unfollowed streamer from the current position onwards
    for (int i = _currentAutoFollowIndex; i < _streamers.length; i++) {
      if (!following.contains(_streamers[i]['username'])) {
        targetIndex = i;
        break;
      }
    }

    // If no unfollowed streamer found from current position, search from the beginning
    if (targetIndex == -1) {
      for (int i = 0;
          i < _currentAutoFollowIndex && i < _streamers.length;
          i++) {
        if (!following.contains(_streamers[i]['username'])) {
          targetIndex = i;
          break;
        }
      }
    }

    // Navigate to the found unfollowed streamer
    if (targetIndex != -1) {
      _pageController.animateToPage(
        targetIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _processNextAutoFollow() async {
    if (!_autoFollowEnabled ||
        !_isAutoFollowActive ||
        _currentAutoFollowIndex >= _streamers.length) {
      _stopAutoFollow();
      return;
    }

    // Skip already followed streamers
    while (_currentAutoFollowIndex < _streamers.length &&
        following.contains(_streamers[_currentAutoFollowIndex]['username'])) {
      _currentAutoFollowIndex++;
    }

    if (_currentAutoFollowIndex >= _streamers.length) {
      _stopAutoFollow();
      return;
    }

    // Start following current streamer
    await _handleAutoFollow(_currentAutoFollowIndex);
  }

  Future<void> _handleAutoFollow(int index) async {
    if (!_autoFollowEnabled || !_isAutoFollowActive) return;

    setState(() {
      _isFollowing = true;
      _pendingFollowIndex = index;
    });

    final streamer = _streamers[index];
    final twitchUrl = 'twitch://stream/${streamer['username']}';
    final webUrl = 'https://twitch.tv/${streamer['username']}';

    try {
      if (await canLaunchUrl(Uri.parse(twitchUrl))) {
        await launchUrl(Uri.parse(twitchUrl));
      } else {
        await launchUrl(Uri.parse(webUrl));
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('follow.launch_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
      // Move to next streamer on error
      _moveToNextAutoFollow();
    } finally {
      if (mounted) {
        setState(() {
          _isFollowing = false;
        });
      }
    }
  }

  void _moveToNextAutoFollow() {
    if (!_autoFollowEnabled || !_isAutoFollowActive) return;

    setState(() {
      _currentAutoFollowIndex++;
      _pendingFollowIndex = null;
    });

    // Process next streamer after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_autoFollowEnabled && _isAutoFollowActive) {
        _processNextAutoFollow();
      }
    });
  }

  void getStreamers() async {
    var result =
        await Dio().get("https://synthrexlabs.com/twgrow/getRandomCampaigns");
    for (var item in result.data["data"]) {
      if (!following.contains(item["UserName"]) &&
          (loginBox.get("loginname") ?? "") != item["UserName"] &&
          _streamers
              .where(
                (element) => element["username"] == item["UserName"],
              )
              .isEmpty) {
        _streamers.add({
          'username': item["UserName"],
          'id': item["UserId"],
          'avatarUrl': item["ProfileImage"],
          'CID': item["CID"]
        });
      }
    }
    setState(() {});

    // Start auto follow if enabled and not already active
    if (_autoFollowEnabled && !_isAutoFollowActive && _streamers.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_autoFollowEnabled && !_isAutoFollowActive) {
          _startAutoFollow();
        }
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _twitchClient.dispose();
    super.dispose();
  }

  Future<void> _initTwitchClient() async {
    _twitchClient = TwitchClient(loginBox.get("token") ?? "");
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && _pendingFollowIndex != null) {
      _lastAppResumeTime = DateTime.now();

      if (_autoFollowEnabled && _isAutoFollowActive) {
        _verifyAutoFollowStatus(_pendingFollowIndex!);
      } else {
        _verifyFollowStatus(_pendingFollowIndex!);
      }
    }
  }

  final loginBox = Hive.box("User");

  Future<void> _verifyFollowStatus(int index) async {
    if (!mounted) return;

    final streamer = _streamers[index];

    try {
      String currentUserId = loginBox.get("userid") ?? "";

      final isFollowing = await _twitchClient.checkFollowsChannel(
        currentUserId,
        (streamer['id']).toString(),
      );
      if (isFollowing && mounted) {
        await _handleSuccessfulFollow(index);
        _pendingFollowIndex = null;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('follow.verify_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _verifyAutoFollowStatus(int index) async {
    if (!mounted) return;

    final streamer = _streamers[index];

    try {
      String currentUserId = loginBox.get("userid") ?? "";

      final isFollowing = await _twitchClient.checkFollowsChannel(
        currentUserId,
        (streamer['id']).toString(),
      );

      if (isFollowing && mounted) {
        await _handleSuccessfulAutoFollow(index);
      } else {
        // If not following, wait a bit and try again or move to next
        Future.delayed(const Duration(seconds: 1), () {
          if (_autoFollowEnabled && _isAutoFollowActive && mounted) {
            _moveToNextAutoFollow();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('follow.verify_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
        // Move to next on error
        _moveToNextAutoFollow();
      }
    }
  }

  Future<void> _handleSuccessfulAutoFollow(int index) async {
    following.add(_streamers[index]['username']);
    followBox.put("Follow", following);
    setState(() {
      _isAddingCoins = true;
    });

    if (!mounted) return;

    final provider = Provider.of<Data>(context, listen: false);
    await Dio().get(
        "https://synthrexlabs.com/twgrow/done/${_streamers[index]["CID"]}");

    await provider.addPoints(15);

    setState(() {
      _isAddingCoins = false;
    });

    if (!mounted) return;
    _showRewardSnackbar(context);

    // Update follow bonus progress
    _updateFollowBonusProgress();

    // Calculate delay needed before next channel
    final now = DateTime.now();
    final timeSinceResume = _lastAppResumeTime != null
        ? now.difference(_lastAppResumeTime!).inMilliseconds
        : 0;

    // Ensure at least 5 seconds have passed since app resume
    final delayNeeded = math.max(0, 6000 - timeSinceResume);

    Future.delayed(Duration(milliseconds: delayNeeded), () {
      if (_autoFollowEnabled && _isAutoFollowActive && mounted) {
        _moveToNextAutoFollow();
      }
    });
  }

  Future<void> _handleFollow(int index, BuildContext context) async {
    if (_isFollowing || _isAddingCoins) return;

    // Check if user is logged in with Twitch
    String? token = loginBox.get("token");
    if (token == null || token.isEmpty) {
      _showTwitchLoginDialog(context, index);
      return;
    }

    setState(() {
      _isFollowing = true;
      _pendingFollowIndex = index;
    });

    final streamer = _streamers[index];
    final twitchUrl = 'twitch://stream/${streamer['username']}';
    final webUrl = 'https://twitch.tv/${streamer['username']}';

    try {
      if (await canLaunchUrl(Uri.parse(twitchUrl))) {
        await launchUrl(Uri.parse(twitchUrl));
      } else {
        await launchUrl(Uri.parse(webUrl));
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('follow.launch_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
      _pendingFollowIndex = null;
    } finally {
      if (mounted) {
        setState(() {
          _isFollowing = false;
        });
      }
    }
  }

  Future<void> _handleSuccessfulFollow(int index) async {
    following.add(_streamers[index]['username']);
    followBox.put("Follow", following);
    setState(() {
      _isAddingCoins = true;
    });

    if (!mounted) return;

    final provider = Provider.of<Data>(context, listen: false);
    await Dio().get(
        "https://synthrexlabs.com/twgrow/done/${_streamers[index]["CID"]}");

    await provider.addPoints(15);

    setState(() {
      _isAddingCoins = false;
    });
    if (!mounted) return;
    _showRewardSnackbar(context);

    // Update follow bonus progress
    _updateFollowBonusProgress();

    _seeOther();
  }

  void _showRewardSnackbar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.monetization_on, color: Color(0xFF9146FF)),
            const SizedBox(width: 8),
            Expanded(
              child: Text('follow.coins_earned'.tr(args: ['15'])),
            )
          ],
        ),
        backgroundColor: const Color(0xFF282842),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _seeOther() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _claimWelcomeBonus() async {
    if (_isClaimingBonus) return;

    setState(() {
      _isClaimingBonus = true;
    });

    try {
      final provider = Provider.of<Data>(context, listen: false);
      await provider.addPoints(50);

      // Mark bonus as claimed
      await loginBox.put("welcomeBonusClaimed", true);

      setState(() {
        _showWelcomeBonus = false;
        _isClaimingBonus = false;
      });

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.celebration, color: Color(0xFF9146FF)),
              const SizedBox(width: 8),
              Expanded(
                child: Text('welcome_bonus.claimed_success'.tr(args: ['50'])),
              ),
            ],
          ),
          backgroundColor: const Color(0xFF282842),
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );

      // Check if user hasn't rated and show rating dialog
      _checkAndShowRatingDialogAfterWelcomeBonus();
    } catch (e) {
      setState(() {
        _isClaimingBonus = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('welcome_bonus.claim_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _checkAndShowRatingDialogAfterWelcomeBonus() async {
    final bool hasRated = loginBox.get("hasRated") ?? false;

    if (!hasRated) {
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => RateAppDialog(
              onRated: () async {
                await loginBox.put("hasRated", true);
              },
            ),
          );
        }
      });
    }
  }

  void _showTwitchLoginDialog(BuildContext context, int index) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          // margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF282842), Color(0xFF32324D)],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon with gradient background
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF9146FF).withValues(alpha: 0.4),
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.login_rounded,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),

                // Title
                Text(
                  'follow.connect_with_twitch'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: -0.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Description
                Text(
                  'follow.login_description'.tr(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // // Benefits
                // Container(
                //   padding: const EdgeInsets.all(16),
                //   decoration: BoxDecoration(
                //     color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                //     borderRadius: BorderRadius.circular(12),
                //     border: Border.all(
                //       color: const Color(0xFF9146FF).withValues(alpha: 0.2),
                //       width: 1,
                //     ),
                //   ),
                //   child: Row(
                //     children: [
                //       const Icon(
                //         Icons.monetization_on,
                //         color: Color(0xFF9146FF),
                //         size: 20,
                //       ),
                //       const SizedBox(width: 8),
                //       Text(
                //         'Earn +15 coins per follow',
                //         style: TextStyle(
                //           color: Colors.white.withValues(alpha: 0.9),
                //           fontSize: 14,
                //           fontWeight: FontWeight.w500,
                //         ),
                //       ),
                //     ],
                //   ),
                // ),

                const SizedBox(height: 16),

                // Buttons
                Column(
                  children: [
                    // Login button
                    Container(
                      width: double.infinity,
                      height: 52,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color:
                                const Color(0xFF9146FF).withValues(alpha: 0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () async {
                            Navigator.pop(context);
                            await _handleTwitchLogin(context, index);
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Center(
                            child: Text(
                              'follow.login_with_twitch'.tr(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Cancel button
                    Container(
                      width: double.infinity,
                      height: 52,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => Navigator.pop(context),
                          borderRadius: BorderRadius.circular(16),
                          child: Center(
                            child: Text(
                              'follow.maybe_later'.tr(),
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleTwitchLogin(BuildContext context, int index) async {
    try {
      var token = await Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => const TwitchLoginScreen(),
      ));

      if (token is String) {
        var result = await TwitchClient(token).getCurrentUser();

        loginBox.put("userid", result.id);
        loginBox.put("displayname", result.displayName);
        loginBox.put("image", result.profileImageUrl);
        loginBox.put("loginname", result.login);
        loginBox.put("month", DateTime.now().month);
        loginBox.put("token", token);
        // Reinitialize Twitch client with new token

        _twitchClient.dispose();
        _twitchClient = TwitchClient(token);

        // Update auto follow widget state since user is now logged in
        _checkAutoFollowWidget();

        // Check and show follow bonus widget for newly logged in user
        _checkFollowBonusForTwitchUser();

        // Now proceed with the follow action
        if (!context.mounted) return;
        _handleFollow(index, context);
      }
    } catch (e) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('auth.login_failed'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E1A),
      body: SafeArea(
        child: Column(
          children: [
            // Auto follow widget for Twitch logged users, welcome bonus for others
            if (_showAutoFollowWidget)
              _buildAutoFollowWidget()
            else if (_showWelcomeBonus)
              _buildWelcomeBonusWidget(),
            // Follow bonus widget for Twitch logged users
            if (_showFollowBonusWidget) _buildFollowBonusWidget(),
            // Main content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _streamers.length,
                physics: _isFollowing || _isAddingCoins
                    ? const NeverScrollableScrollPhysics()
                    : const AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return _autoFollowEnabled
                      ? _buildEmptyCard()
                      : _buildProfileCard(_streamers[index], index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCard(Map<String, dynamic> streamer, int index) {
    // Adjust margins based on whether widgets are shown
    final bool hasTopWidget =
        _showWelcomeBonus || _showAutoFollowWidget || _showFollowBonusWidget;
    final double topMargin = hasTopWidget ? 10 : 20;
    final double bottomMargin = hasTopWidget ? 10 : 20;

    return Container(
      margin: EdgeInsets.fromLTRB(20, topMargin, 20, bottomMargin),
      decoration: BoxDecoration(
        color: const Color(0xFF282842),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16),
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              Container(
                width: 125,
                height: 125,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF343450),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 16,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: Image.network(
                    streamer['avatarUrl'],
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.person,
                          size: 100, color: Colors.white54);
                    },
                  ),
                ),
              ),
              SizedBox(height: 20),
              Text(
                streamer['username'],
                style: TextStyle(
                  color: Colors.white,
                  fontSize: hasTopWidget ? 24 : 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 12),
              if (_isFollowing || _isAddingCoins)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFF9146FF),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isFollowing
                            ? 'follow.following'.tr()
                            : 'follow.adding_coins'.tr(),
                        style: const TextStyle(
                          color: Color(0xFF9146FF),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.monetization_on,
                        size: 20,
                        color: Color(0xFF9146FF),
                      ),
                      SizedBox(width: 4),
                      Text(
                        'follow.coins_reward'.tr(),
                        style: const TextStyle(
                          color: Color(0xFF9146FF),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: following.contains(streamer['username'])
                    ? // Show only "See Other" for followed channels
                    SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: OutlinedButton(
                          onPressed: (_isFollowing || _isAddingCoins)
                              ? null
                              : _seeOther,
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(
                              color: (_isFollowing || _isAddingCoins)
                                  ? const Color(0xFF9146FF)
                                      .withValues(alpha: 0.5)
                                  : const Color(0xFF9146FF),
                              width: 2,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: Text(
                            'follow.see_other'.tr(),
                            style: TextStyle(
                              color: (_isFollowing || _isAddingCoins)
                                  ? const Color(0xFF9146FF)
                                      .withValues(alpha: 0.5)
                                  : const Color(0xFF9146FF),
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                    : // Show both buttons in a row for unfollowed channels
                    Row(
                        children: [
                          Expanded(
                            child: SizedBox(
                              height: 50,
                              child: OutlinedButton(
                                onPressed: (_isFollowing || _isAddingCoins)
                                    ? null
                                    : _seeOther,
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                    color: (_isFollowing || _isAddingCoins)
                                        ? const Color(0xFF9146FF)
                                            .withValues(alpha: 0.5)
                                        : const Color(0xFF9146FF),
                                    width: 2,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                ),
                                child: Text(
                                  'follow.see_other'.tr(),
                                  style: TextStyle(
                                    color: (_isFollowing || _isAddingCoins)
                                        ? const Color(0xFF9146FF)
                                            .withValues(alpha: 0.5)
                                        : const Color(0xFF9146FF),
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: SizedBox(
                              height: 50,
                              child: ElevatedButton(
                                onPressed: (_isFollowing || _isAddingCoins)
                                    ? null
                                    : () => _handleFollow(index, context),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF9146FF),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                ),
                                child: _isFollowing || _isAddingCoins
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      )
                                    : Text(
                                        'follow.follow'.tr(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeBonusWidget() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF9146FF).withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.celebration,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'welcome_bonus.title'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'welcome_bonus.description'.tr(args: ['50']),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isClaimingBonus ? null : _claimWelcomeBonus,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF9146FF),
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 0,
              ),
              child: _isClaimingBonus
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF9146FF),
                        ),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.monetization_on,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'welcome_bonus.claim_button'.tr(args: ['50']),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoFollowWidget() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF9146FF).withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.auto_mode,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'auto_follow.title'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Switch(
                value: _autoFollowEnabled,
                onChanged: _toggleAutoFollow,
                activeColor: Colors.white,
                activeTrackColor: Colors.white.withValues(alpha: 0.3),
                inactiveThumbColor: Colors.white.withValues(alpha: 0.7),
                inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFollowBonusWidget() {
    final double progress = _followBonusProgress / 30.0;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.fromLTRB(16, 4, 16, 4),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF00C851), Color(0xFF00A843)],
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00C851).withValues(alpha: 0.3),
            blurRadius: 9,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Container(
              //   padding: const EdgeInsets.all(8),
              //   decoration: BoxDecoration(
              //     color: Colors.white.withValues(alpha: 0.2),
              //     borderRadius: BorderRadius.circular(8),
              //   ),
              //   child: const Icon(
              //     Icons.star,
              //     color: Colors.white,
              //     size: 20,
              //   ),
              // ),
              // const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'follow_bonus.title'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    // Progress bar
                    Row(
                      children: [
                        Text(
                          '$_followBonusProgress/30',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(
                          width: 6,
                        ),
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: progress,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.monetization_on,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '100',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyCard() {
    final bool showCurrentChannel =
        _isAutoFollowActive && _currentAutoFollowIndex < _streamers.length;

    final currentStreamer =
        showCurrentChannel ? _streamers[_currentAutoFollowIndex] : null;

    // Adjust margins based on whether auto follow widget is shown
    final double topMargin = _showAutoFollowWidget ? 10 : 20;
    final double bottomMargin = _showAutoFollowWidget ? 10 : 20;

    return Container(
      margin: EdgeInsets.fromLTRB(20, topMargin, 20, bottomMargin),
      decoration: BoxDecoration(
        color: const Color(0xFF282842),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16),
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              if (showCurrentChannel && currentStreamer != null) ...[
                Container(
                  width: 125,
                  height: 125,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFF343450),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: Image.network(
                      currentStreamer['avatarUrl'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(Icons.person,
                            size: 100, color: Colors.white54);
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  currentStreamer['username'],
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: _showAutoFollowWidget ? 24 : 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: _showAutoFollowWidget ? 12 : 20),
                if (_isFollowing || _isAddingCoins)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Color(0xFF9146FF),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isFollowing
                              ? 'auto_follow.opening'.tr()
                              : 'auto_follow.verifying'.tr(),
                          style: const TextStyle(
                            color: Color(0xFF9146FF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.auto_mode,
                          size: 20,
                          color: Color(0xFF9146FF),
                        ),
                        SizedBox(width: 4),
                        Text(
                          'auto_follow.active_description'.tr(),
                          style: const TextStyle(
                            color: Color(0xFF9146FF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ] else ...[
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(0xFF9146FF).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.auto_mode,
                    size: 40,
                    color: Color(0xFF9146FF),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'auto_follow.enabled_title'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                )
              ],
            ],
          ),
        ),
      ),
    );
  }
}
