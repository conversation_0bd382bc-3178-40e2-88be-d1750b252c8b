import 'dart:convert';
import 'dart:math';

import 'package:crypton/crypton.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

class Data extends ChangeNotifier {
  int points = 0;
  List<Map<String, dynamic>> campaigns = [];
  List<Package> packages = [];
  void addPackage(Package package) {
    packages.add(package);
    notifyListeners();
  }

  RSAKeypair encryption = RSAKeypair(RSAPrivateKey.fromString(utf8.decode(
      base64.decode(
          '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'))));

  Function changeIndex = (int index) {};

  Future addPoints(int coin) async {
    points += coin;
    notifyListeners();
    String key = await getKey();

    String uid = loginBox.get("UID") ?? "";

    await Dio().post("https://synthrexlabs.com/twgrow/addCoins", data: {
      "data": jsonEncode(encryption.publicKey
          .encrypt(jsonEncode({"Key": key, "Coins": coin, "UID": uid})))
    });
  }

  final loginBox = Hive.box("User");

  void addCampaign(
      String package, String username, int current, int target, String cid) {
    campaigns.add({
      'title': package,
      'username': username,
      'current': current,
      'target': target,
      'cid': cid
    });
    notifyListeners();
  }

  void resetProvider() {
    campaigns = [];
    points = 0;
  }

  void addOfflinePoints(int coins) {
    points += coins;
    notifyListeners();
  }

  void refreshPoints() async {
    String uid = loginBox.get("UID") ?? "";
    var result = await Dio().get("https://synthrexlabs.com/twgrow/coins/$uid");
    points = result.data["data"]["Coins"];
    notifyListeners();
  }

  Future<String> getKey() async {
    String chars =
        'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    String getRandomString(int length) =>
        String.fromCharCodes(Iterable.generate(
            length, (_) => chars.codeUnitAt(Random().nextInt(chars.length))));

    String uid = loginBox.get("UID") ?? "";
    var result =
        await Dio().get("https://synthrexlabs.com/twgrow/coinKey/$uid");

    String key = utf8
        .decode(
            base64.decode(encryption.privateKey.decrypt(result.data["key"])))
        .substring(16, 24);

    key = getRandomString(5) + key + getRandomString(16);
    key = base64.encode(utf8.encode(key));

    return key;
  }

  void refreshCampaigns() async {
    String uid = loginBox.get("UID") ?? "";
    var result =
        await Dio().get("https://synthrexlabs.com/twgrow/campaigns/$uid");
    for (var item in result.data["data"]) {
      campaigns.add({
        'title': item["Package"],
        'username': item["UserName"],
        'current': item["Followers"] - item["Remaining"],
        'target': item["Followers"],
        'cid': item["CID"]
      });
    }
    notifyListeners();
  }

  void sendDeleteRequest(String cid) async {
    String key = await getKey();
    Dio().post("https://synthrexlabs.com/twgrow/deleteCampaign", data: {
      "data": jsonEncode(encryption.publicKey.encrypt(jsonEncode(
          {"CID": cid, "UID": loginBox.get("UID") ?? "", "Key": key})))
    });
  }

  void deleteCampaign(String cid) {
    sendDeleteRequest(cid);
    campaigns.removeWhere(
      (element) => element["cid"] == cid,
    );
    notifyListeners();
  }

  // Store selected channel for promote screen
  void storeSelectedChannel(
      String username, String userId, String profileImage) {
    loginBox.put("selected_channel_username", username);
    loginBox.put("selected_channel_userid", userId);
    loginBox.put("selected_channel_image", profileImage);
  }

  // Get stored selected channel
  Map<String, String> getSelectedChannel() {
    return {
      'username': loginBox.get("selected_channel_username") ?? "",
      'userId': loginBox.get("selected_channel_userid") ?? "",
      'profileImage': loginBox.get("selected_channel_image") ?? "",
    };
  }

  // Clear selected channel
  void clearSelectedChannel() {
    loginBox.delete("selected_channel_username");
    loginBox.delete("selected_channel_userid");
    loginBox.delete("selected_channel_image");
  }
}
