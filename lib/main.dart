// main.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:twitch/dashboard.dart';
import 'package:twitch/loading_screen.dart';
import 'package:twitch/provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/overlay_widget.dart';

// overlay entry point
@pragma("vm:entry-point")
void overlayMain() {
  runApp(const MaterialApp(
    debugShowCheckedModeBanner: false,
    home: OverlayWidget(),
  ));
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await Hive.initFlutter();
  await Hive.openBox('User');
  await Hive.openBox('Follow');

  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('en'), // English
        Locale('ru'), // Russian
        Locale('pt'), // Portuguese
        Locale('es'), // Spanish
        Locale('de'), // German
        Locale('ja'), // Japanese
        Locale('fr'), // French
        Locale('zh'), // Chinese
        Locale('pl'), // Polish
        Locale('it'), // Italian
        Locale('tr'), // Turkish
        Locale('th'), // Thai
        Locale('uk'), // Ukrainian
        Locale('cs'), // Czech
        Locale('ar'), // Arabic
      ],
      path: 'assets/translations', // Path to your translation files
      fallbackLocale: const Locale('en'),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final loginBox = Hive.box("User");
  
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: Color(0xFF0E0E1A),
      systemNavigationBarIconBrightness: Brightness.light,
    ));
    
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => Data()),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'TwGrow',
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        theme: ThemeData(
          primaryColor: const Color(0xFF9146FF),
          scaffoldBackgroundColor: const Color(0xFF0E0E1A),
          useMaterial3: true,
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF282842),
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          drawerTheme: const DrawerThemeData(
            backgroundColor: Color(0xFF282842),
          ),
        ),
        home: loginBox.get("month") == DateTime.now().month
            ? const DashboardScreen()
            : const LoadingScreen(),
      ),
    );
  }
}