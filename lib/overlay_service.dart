import 'dart:async';
import 'package:flutter/services.dart';

class OverlayService {
  static const MethodChannel _channel = MethodChannel('twgrow/overlay');
  static Timer? _timer;
  static bool _isOverlayVisible = false;
  static int _remainingSeconds = 15;
  static VoidCallback? _onReturnCallback;
  static VoidCallback? _onCancelCallback;

  static Future<bool> checkOverlayPermission() async {
    try {
      final bool hasPermission = await _channel.invokeMethod('checkOverlayPermission');
      return hasPermission;
    } catch (e) {
      return false;
    }
  }

  static Future<void> requestOverlayPermission() async {
    try {
      await _channel.invokeMethod('requestOverlayPermission');
    } catch (e) {
      // Handle error
    }
  }

  static Future<void> showTimerOverlay({
    required VoidCallback onReturn,
    required VoidCallback onCancel,
  }) async {
    if (_isOverlayVisible) return;

    _onReturnCallback = onReturn;
    _onCancelCallback = onCancel;
    _remainingSeconds = 15;
    _isOverlayVisible = true;

    // Set up method call handler for overlay events
    _channel.setMethodCallHandler(_handleMethodCall);

    // Show overlay
    await _showOverlay();

    // Start countdown timer
    _startTimer();
  }

  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onReturnNow':
        _returnNow();
        break;
      case 'onCancel':
        _cancelOverlay();
        break;
      case 'onAppResumed':
        // App has been brought to foreground
        _returnNow();
        break;
    }
  }

  static Future<void> _showOverlay() async {
    try {
      await _channel.invokeMethod('showOverlay', {
        'remainingSeconds': _remainingSeconds,
      });
    } catch (e) {
      // Handle error
    }
  }

  static void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _remainingSeconds--;

      if (_remainingSeconds <= 0) {
        _returnNow();
      } else {
        _updateOverlay();
      }
    });
  }

  static Future<void> _updateOverlay() async {
    if (!_isOverlayVisible) return;

    try {
      await _channel.invokeMethod('updateOverlay', {
        'remainingSeconds': _remainingSeconds,
      });
    } catch (e) {
      // Handle error
    }
  }

  static void _cancelOverlay() {
    _hideOverlay();
    _onCancelCallback?.call();
  }

  static void _returnNow() {
    _hideOverlay();
    _onReturnCallback?.call();
  }

  static void _hideOverlay() {
    _timer?.cancel();
    _timer = null;
    _isOverlayVisible = false;

    try {
      _channel.invokeMethod('hideOverlay');
    } catch (e) {
      // Handle error
    }
  }

  static void dispose() {
    _hideOverlay();
    _onReturnCallback = null;
    _onCancelCallback = null;
  }
}


