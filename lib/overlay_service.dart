import 'dart:async';
import 'dart:isolate';
import 'dart:ui';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';

class OverlayService {
  static Timer? _timer;
  static bool _isOverlayVisible = false;
  static int _remainingSeconds = 15;
  static VoidCallback? _onReturnCallback;
  static VoidCallback? _onCancelCallback;
  static ReceivePort? _port;
  static const String _foregroundPortName = "foreground_port";

  static Future<bool> checkOverlayPermission() async {
    return await FlutterOverlayWindow.isPermissionGranted();
  }

  static Future<void> requestOverlayPermission() async {
    await FlutterOverlayWindow.requestPermission();
  }

  static Future<void> showTimerOverlay({
    required VoidCallback onReturn,
    required VoidCallback onCancel,
  }) async {
    if (_isOverlayVisible) return;

    _onReturnCallback = onReturn;
    _onCancelCallback = onCancel;
    _remainingSeconds = 15;
    _isOverlayVisible = true;

    // Setup isolate communication
    _setupIsolateComm();

    // Show overlay
    await _showOverlay();

    // Start countdown timer
    _startTimer();
  }

  static void _setupIsolateComm() {
    _port = ReceivePort();
    IsolateNameServer.removePortNameMapping(_foregroundPortName);
    IsolateNameServer.registerPortWithName(_port!.sendPort, _foregroundPortName);

    _port!.listen((message) {
      if (message == "return_now") {
        _returnNow();
      } else if (message == "cancel") {
        _cancelOverlay();
      }
    });
  }

  static Future<void> _showOverlay() async {
    await FlutterOverlayWindow.showOverlay(
      height: 120,
      width: WindowSize.matchParent,
      alignment: OverlayAlignment.bottomCenter,
      overlayTitle: "TwGrow Auto Follow",
      overlayContent: "Returning to app in $_remainingSeconds seconds",
    );
  }

  static void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _remainingSeconds--;

      if (_remainingSeconds <= 0) {
        _returnNow();
      } else {
        _updateOverlay();
      }
    });
  }

  static Future<void> _updateOverlay() async {
    if (!_isOverlayVisible) return;

    // Send update to overlay
    FlutterOverlayWindow.shareData({
      'remainingSeconds': _remainingSeconds,
    });
  }

  static void _cancelOverlay() {
    _hideOverlay();
    _onCancelCallback?.call();
  }

  static void _returnNow() {
    _hideOverlay();
    _onReturnCallback?.call();
  }

  static void _hideOverlay() {
    _timer?.cancel();
    _timer = null;
    _isOverlayVisible = false;
    FlutterOverlayWindow.closeOverlay();

    if (_port != null) {
      IsolateNameServer.removePortNameMapping(_foregroundPortName);
      _port!.close();
      _port = null;
    }
  }

  static void dispose() {
    _hideOverlay();
    _onReturnCallback = null;
    _onCancelCallback = null;
  }
}


