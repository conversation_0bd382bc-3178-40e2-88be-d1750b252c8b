import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/provider.dart';

class BuyScreen extends StatefulWidget {
  const BuyScreen({super.key});

  @override
  State<BuyScreen> createState() => _BuyScreenState();
}

class _BuyScreenState extends State<BuyScreen> {
  bool purchasing = false;
  int? selectedPackageIndex;

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<Data>(context);
    List<Package> packages = provider.packages;
    return Container(
      color: const Color(0xFF0E0E1A),
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildCoinPackage(
            index: 0,
            coins: 500,
            price: packages.isNotEmpty ? packages[0].storeProduct.priceString : "\$1.99",
            tag: 'packages.starter.tag'.tr(),
            discount: 0,
            package: packages.isNotEmpty ? packages[0] : null,
          ),
          _buildCoinPackage(
            index: 1,
            coins: 1500,
            price: packages.isNotEmpty ? packages[1].storeProduct.priceString : "\$4.99",
            tag: 'packages.growth.tag'.tr(),
            discount: 16,
            isPopular: true,
            package: packages.isNotEmpty ? packages[1] : null,
          ),
          // _buildCoinPackage(
          //   index: 2,
          //   coins: 4500,
          //   price: packages.isNotEmpty ? packages[2].storeProduct.priceString : "\$12.99",
          //   tag: 'packages.accelerate.tag'.tr(),
          //   discount: 20,
          //   package: packages.isNotEmpty ? packages[2] : null,
          // ),
          _buildCoinPackage(
            index: 3,
            coins: 9000,
            price: packages.isNotEmpty ? packages[3].storeProduct.priceString : "\$24.99",
            tag: 'packages.pro.tag'.tr(),
            discount: 30,
            package: packages.isNotEmpty ? packages[3] : null,
          ),
          _buildCoinPackage(
            index: 4,
            coins: 24000,
            price: packages.isNotEmpty ? packages[4].storeProduct.priceString : "\$49.99",
            tag: 'packages.ultimate.tag'.tr(),
            discount: 48,
            package: packages.isNotEmpty ? packages[4] : null,
          ),
        ],
      ),
    );
  }

  Future<void> _handlePurchase(Package package, int coins, int index) async {
    if (purchasing) return;

    setState(() {
      purchasing = true;
      selectedPackageIndex = index;
    });

    try {
      await Purchases.purchasePackage(package);
      if (!mounted) return;
      
      final provider = Provider.of<Data>(context, listen: false);
      provider.addPoints(coins);
      
      // Show success snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  'buy.purchase_success'.tr(args: [coins.toString()]),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } on PlatformException catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Text('buy.purchase_failed'.tr(args: [e.message ?? ''])),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          purchasing = false;
          selectedPackageIndex = null;
        });
      }
    }
  }

  Widget _buildCoinPackage({
    required int index,
    required int coins,
    required String price,
    required String tag,
    required int discount,
    required Package? package,
    bool isPopular = false,
  }) {
    bool isLoading = purchasing && selectedPackageIndex == index;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF282842),
        borderRadius: BorderRadius.circular(12),
        border: isPopular
            ? Border.all(color: const Color(0xFF9146FF), width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha:0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: package == null || purchasing
              ? null
              : () => _handlePurchase(package, coins, index),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF343450),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              tag,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (discount > 0) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF9146FF).withValues(alpha:0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '-$discount%',
                                style: const TextStyle(
                                  color: Color(0xFF9146FF),
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.monetization_on,
                            color: Color(0xFF9146FF),
                            size: 24,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            coins.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: EdgeInsets.symmetric(
                    horizontal: isLoading ? 12 : 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isLoading 
                      ? const Color(0xFF9146FF).withValues(alpha:0.8)
                      : const Color(0xFF9146FF),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isLoading) ...[
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        isLoading ? 'buy.buying'.tr() : price.replaceAll(".00", ""),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}